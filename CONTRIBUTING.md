# 贡献指南

感谢您对 User-DF 项目的关注！我们欢迎各种形式的贡献，包括但不限于代码、文档、测试、问题报告和功能建议。

## 🤝 贡献方式

### 1. 代码贡献
- 修复 Bug
- 添加新功能
- 性能优化
- 代码重构

### 2. 文档贡献
- 改进现有文档
- 添加使用示例
- 翻译文档
- 编写教程

### 3. 测试贡献
- 编写单元测试
- 添加集成测试
- 性能测试
- 兼容性测试

### 4. 其他贡献
- 报告 Bug
- 提出功能建议
- 参与讨论
- 代码审查

## 🚀 开始贡献

### 1. 环境准备

```bash
# Fork 项目到您的 GitHub 账户
# 克隆您的 Fork
git clone https://github.com/your-username/User-DF.git
cd User-DF

# 添加上游仓库
git remote add upstream https://github.com/user-df/User-DF.git

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装开发依赖
pip install -e .[dev,test]

# 安装 pre-commit hooks
pre-commit install
```

### 2. 开发流程

```bash
# 创建功能分支
git checkout -b feature/your-feature-name

# 进行开发
# ... 编写代码 ...

# 运行测试
pytest

# 代码格式化
black .
isort .

# 类型检查
mypy shared/ services/

# 提交更改
git add .
git commit -m "feat: add your feature description"

# 推送到您的 Fork
git push origin feature/your-feature-name

# 创建 Pull Request
```

## 📝 代码规范

### 1. Python 代码风格

我们使用以下工具确保代码质量：

- **Black**: 代码格式化
- **isort**: 导入排序
- **flake8**: 代码检查
- **mypy**: 类型检查

### 2. 命名规范

```python
# 变量和函数：snake_case
user_count = 100
def process_user_data():
    pass

# 类名：PascalCase
class UserDataProcessor:
    pass

# 常量：UPPER_SNAKE_CASE
MAX_BATCH_SIZE = 1000

# 私有成员：前缀下划线
class MyClass:
    def __init__(self):
        self._private_var = None
        self.__very_private = None
```

### 3. 文档字符串

```python
def process_users(users: List[Dict], batch_size: int = 1000) -> Dict:
    """
    处理用户数据批次。
    
    Args:
        users: 用户数据列表，每个元素包含用户信息
        batch_size: 批处理大小，默认 1000
        
    Returns:
        包含处理结果的字典，包括成功数量、失败数量等
        
    Raises:
        ValueError: 当 batch_size 小于等于 0 时
        DatabaseError: 当数据库连接失败时
        
    Example:
        >>> users = [{"uid": 1, "pids": [1, 2, 3]}]
        >>> result = process_users(users, batch_size=100)
        >>> print(result["success_count"])
        1
    """
    if batch_size <= 0:
        raise ValueError("batch_size must be positive")
    
    # 实现逻辑...
    return {"success_count": len(users), "error_count": 0}
```

### 4. 错误处理

```python
import logging
from shared.core.exceptions import UserDFException

logger = logging.getLogger(__name__)

def risky_operation():
    try:
        # 可能失败的操作
        result = dangerous_function()
        return result
    except SpecificException as e:
        logger.error(f"Specific error occurred: {e}")
        raise UserDFException(f"Operation failed: {e}") from e
    except Exception as e:
        logger.exception("Unexpected error occurred")
        raise UserDFException("Unexpected error") from e
```

## 🧪 测试指南

### 1. 测试结构

```
tests/
├── unit/                    # 单元测试
│   ├── shared/
│   └── services/
├── integration/             # 集成测试
│   ├── test_orc_mongodb.py
│   └── test_user_vector.py
├── fixtures/                # 测试数据
└── conftest.py             # pytest 配置
```

### 2. 编写测试

```python
import pytest
from unittest.mock import Mock, patch
from services.orc_mongodb_service.processor import ORCProcessor

class TestORCProcessor:
    """ORC 处理器测试类"""
    
    @pytest.fixture
    def processor(self):
        """创建处理器实例"""
        config = {
            "batch_size": 100,
            "max_memory_usage": "1GB"
        }
        return ORCProcessor(config)
    
    def test_process_batch_success(self, processor):
        """测试批处理成功场景"""
        # 准备测试数据
        test_data = [
            {"uid": 1, "pids": [1, 2, 3]},
            {"uid": 2, "pids": [4, 5, 6]}
        ]
        
        # 执行测试
        result = processor.process_batch(test_data)
        
        # 验证结果
        assert result["success_count"] == 2
        assert result["error_count"] == 0
    
    def test_process_batch_with_invalid_data(self, processor):
        """测试处理无效数据"""
        test_data = [
            {"uid": "invalid", "pids": [1, 2, 3]}
        ]
        
        with pytest.raises(ValueError, match="Invalid user ID"):
            processor.process_batch(test_data)
    
    @patch('services.orc_mongodb_service.processor.mongodb_client')
    def test_process_batch_database_error(self, mock_mongodb, processor):
        """测试数据库错误处理"""
        # 模拟数据库错误
        mock_mongodb.insert_many.side_effect = Exception("Database error")
        
        test_data = [{"uid": 1, "pids": [1, 2, 3]}]
        
        with pytest.raises(Exception, match="Database error"):
            processor.process_batch(test_data)
```

### 3. 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/unit/services/test_orc_processor.py

# 运行特定测试类
pytest tests/unit/services/test_orc_processor.py::TestORCProcessor

# 运行特定测试方法
pytest tests/unit/services/test_orc_processor.py::TestORCProcessor::test_process_batch_success

# 生成覆盖率报告
pytest --cov=shared --cov=services --cov-report=html

# 运行性能测试
pytest -m slow

# 跳过集成测试
pytest -m "not integration"
```

## 📋 提交规范

### 1. 提交消息格式

我们使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 2. 提交类型

- **feat**: 新功能
- **fix**: Bug 修复
- **docs**: 文档更新
- **style**: 代码格式化（不影响功能）
- **refactor**: 代码重构
- **perf**: 性能优化
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

### 3. 提交示例

```bash
# 新功能
git commit -m "feat(orc-processor): add batch processing support"

# Bug 修复
git commit -m "fix(mongodb): resolve connection timeout issue"

# 文档更新
git commit -m "docs: update API documentation for vector service"

# 性能优化
git commit -m "perf(vector-compute): optimize PCA computation"

# 重大变更
git commit -m "feat!: change user vector dimension from 512 to 256

BREAKING CHANGE: user vector dimension changed from 512 to 256.
Existing vectors need to be recomputed."
```

## 🔍 Pull Request 指南

### 1. PR 标题

使用与提交消息相同的格式：

```
feat(orc-processor): add batch processing support
```

### 2. PR 描述模板

```markdown
## 变更类型
- [ ] Bug 修复
- [ ] 新功能
- [ ] 重大变更
- [ ] 文档更新
- [ ] 性能优化
- [ ] 其他

## 变更描述
简要描述此 PR 的变更内容。

## 相关 Issue
Fixes #123
Closes #456

## 测试
- [ ] 添加了新的测试
- [ ] 所有测试通过
- [ ] 手动测试通过

## 检查清单
- [ ] 代码遵循项目规范
- [ ] 添加了必要的文档
- [ ] 更新了 CHANGELOG.md
- [ ] 通过了所有检查

## 截图（如适用）
添加截图或 GIF 展示变更效果。

## 其他说明
任何其他相关信息。
```

### 3. PR 审查流程

1. **自动检查**: CI/CD 流水线自动运行测试和代码检查
2. **代码审查**: 至少需要一位维护者审查
3. **测试验证**: 确保所有测试通过
4. **文档更新**: 如需要，更新相关文档
5. **合并**: 审查通过后合并到主分支

## 🐛 问题报告

### 1. Bug 报告模板

```markdown
## Bug 描述
简要描述遇到的问题。

## 复现步骤
1. 执行 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

## 期望行为
描述您期望发生的情况。

## 实际行为
描述实际发生的情况。

## 环境信息
- OS: [e.g. Ubuntu 20.04]
- Python 版本: [e.g. 3.8.10]
- User-DF 版本: [e.g. 2.0.0]
- MongoDB 版本: [e.g. 4.4.6]
- Milvus 版本: [e.g. 2.5.0]

## 日志信息
```
粘贴相关的日志信息
```

## 其他信息
添加任何其他有助于解决问题的信息。
```

### 2. 功能请求模板

```markdown
## 功能描述
简要描述您希望添加的功能。

## 问题背景
描述这个功能要解决的问题。

## 解决方案
描述您希望的解决方案。

## 替代方案
描述您考虑过的其他解决方案。

## 其他信息
添加任何其他相关信息或截图。
```

## 🏆 贡献者认可

我们重视每一位贡献者的努力：

- **代码贡献者**: 在 README 中列出
- **文档贡献者**: 在文档中署名
- **测试贡献者**: 在测试文档中感谢
- **问题报告者**: 在 Issue 中感谢

## 📞 获取帮助

如果您在贡献过程中遇到问题：

1. **查看文档**: 首先查看项目文档
2. **搜索 Issues**: 查看是否有类似问题
3. **提问**: 在 GitHub Discussions 中提问
4. **联系维护者**: 发送邮件到 <EMAIL>

## 📜 行为准则

我们致力于为所有人提供友好、安全和欢迎的环境。请遵循以下准则：

- **尊重他人**: 尊重不同的观点和经验
- **建设性反馈**: 提供有建设性的批评和建议
- **协作精神**: 帮助他人学习和成长
- **包容性**: 欢迎来自不同背景的贡献者

违反行为准则的行为将不被容忍，可能导致从项目中被移除。

## 🎉 感谢

感谢您考虑为 User-DF 项目做出贡献！您的参与使这个项目变得更好。

---

*最后更新: 2024-07-29*
